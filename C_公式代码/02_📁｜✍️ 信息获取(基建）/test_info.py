from info_processor import InfoProcessor

def test_info_table_full_process():
    # 初始化处理器
    processor = InfoProcessor()
    
    # 测试数据（原始源信息）
    raw_data = {
        "原始商品名称": "  宠物零食大礼包 100g  "  # 带空格，用于测试清洗
    }
    
    print("===== 开始全流程处理 =====")
    processor.full_process(raw_data)
    print("\n===== 处理结束 =====")

if __name__ == "__main__":
    test_info_table_full_process()
    input("按任意键退出...")
