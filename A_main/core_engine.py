"""
核心数据处理引擎
统一管理12个表格的数据流转、字段关联和公式计算
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from A_连接MongoDB.MongoDB_connector import MongoDBConnector

class CoreEngine:
    """核心数据处理引擎"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.mongo_connector = MongoDBConnector()
        self.table_processors = {}  # 存储各表格处理器
        self.field_cache = {}  # 字段计算缓存
        self.dependency_graph = {}  # 依赖关系图
        
        # 表格处理顺序（按依赖关系排序）
        self.processing_order = [
            "🧠 逻辑表（底库）",
            "📁 信息获取（基建）", 
            "🎫 优惠券管理（底库）",
            "Ⓜ️ 品牌识别（基建）",
            "🛍️ 品类管理（基建）",
            "🔪 SKU解构（基建）",
            "🧪 规格解构（基建）",
            "💰 价格解构（基建）",
            "💻 单价计算（基建）",
            "🔥 价格热度计算（基建）",
            "🐤 SKU重组-网站专用（基建）",
            "🐽 SKU重组-社群专用（基建）",
            "🤖 中转表（工具）"
        ]
        
        self._init_processors()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('CoreEngine')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _init_processors(self):
        """初始化各表格处理器"""
        try:
            # 动态导入各表格处理器
            for table_name in self.processing_order:
                processor_module = self._get_processor_module(table_name)
                if processor_module:
                    self.table_processors[table_name] = processor_module
                    self.logger.info(f"✅ 已加载处理器: {table_name}")
        except Exception as e:
            self.logger.error(f"❌ 初始化处理器失败: {e}")
    
    def _get_processor_module(self, table_name: str):
        """根据表格名称获取对应的处理器模块"""
        # 映射表格名称到文件夹名称
        folder_mapping = {
            "🧠 逻辑表（底库）": "01_🧠 逻辑表（底库）",
            "📁 信息获取（基建）": "02_📁｜✍️ 信息获取(基建）",
            "🎫 优惠券管理（底库）": "03_🎫 优惠券管理（底库）",
            "Ⓜ️ 品牌识别（基建）": "04_Ⓜ️｜✍️ 品牌识别（基建）",
            "🛍️ 品类管理（基建）": "05_🛍️｜✍️ 品类｜口味｜制作工艺 ｜ 使用对象 ｜ 成长期（基建）",
            "🔪 SKU解构（基建）": "06_🔪｜✍️ sku解构(基建)",
            "🧪 规格解构（基建）": "09_🧪 规格解构（基建）",
            "💰 价格解构（基建）": "10_💰 价格解构（基建）",
            "💻 单价计算（基建）": "11_💻 单价计算（基建）",
            "🔥 价格热度计算（基建）": "12_🔥 价格热度计算（基建）",
            "🐤 SKU重组-网站专用（基建）": "07_🐤 sku重组-网站专用（基建）",
            "🐽 SKU重组-社群专用（基建）": "08_🐽 sku重组-社群专用 (基建)",
            "🤖 中转表（工具）": "13_🤖｜✍️ 中转表（工具）"
        }
        
        folder_name = folder_mapping.get(table_name)
        if not folder_name:
            return None
            
        try:
            # 动态导入处理器
            module_path = f"C_公式代码.{folder_name}"
            if table_name == "📁 信息获取（基建）":
                from C_公式代码.02_📁｜✍️ 信息获取(基建） import info_processor
                return info_processor.InfoProcessor()
            elif table_name == "🧠 逻辑表（底库）":
                from C_公式代码.01_🧠 逻辑表（底库） import logic_processor
                return logic_processor.LogicProcessor()
            # 其他处理器待实现
            else:
                return None
        except ImportError as e:
            self.logger.warning(f"⚠️ 无法导入处理器 {table_name}: {e}")
            return None
    
    def process_data_flow(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理完整的数据流
        从原始数据到最终输出的完整流程
        """
        self.logger.info("🚀 开始数据流处理")
        
        try:
            # 连接MongoDB
            if not self.mongo_connector.connect():
                raise Exception("MongoDB连接失败")
            
            # 按顺序处理各表格
            processing_results = {}
            
            for table_name in self.processing_order:
                self.logger.info(f"📊 处理表格: {table_name}")
                
                processor = self.table_processors.get(table_name)
                if not processor:
                    self.logger.warning(f"⚠️ 跳过未实现的处理器: {table_name}")
                    continue
                
                # 处理当前表格
                result = self._process_table(table_name, processor, raw_data, processing_results)
                processing_results[table_name] = result
                
                self.logger.info(f"✅ 完成表格处理: {table_name}")
            
            self.logger.info("🎉 数据流处理完成")
            return processing_results
            
        except Exception as e:
            self.logger.error(f"❌ 数据流处理失败: {e}")
            raise
    
    def _process_table(self, table_name: str, processor, raw_data: Dict, 
                      previous_results: Dict) -> Dict[str, Any]:
        """处理单个表格"""
        try:
            # 根据表格类型调用不同的处理方法
            if table_name == "📁 信息获取（基建）":
                return processor.full_process(raw_data)
            elif table_name == "🧠 逻辑表（底库）":
                return self._process_logic_table(processor)
            else:
                # 其他表格的处理逻辑待实现
                return {"status": "待实现", "table": table_name}
                
        except Exception as e:
            self.logger.error(f"❌ 表格 {table_name} 处理失败: {e}")
            return {"status": "失败", "error": str(e)}
    
    def _process_logic_table(self, processor) -> Dict[str, Any]:
        """处理逻辑表"""
        # 逻辑表主要提供基础规则，不需要特殊处理
        return {"status": "完成", "type": "逻辑表"}
    
    def get_field_value(self, table_name: str, field_name: str, record_id: str) -> Any:
        """获取字段值（支持缓存）"""
        cache_key = f"{table_name}.{field_name}.{record_id}"
        
        if cache_key in self.field_cache:
            return self.field_cache[cache_key]
        
        # 计算字段值
        processor = self.table_processors.get(table_name)
        if processor and hasattr(processor, 'get_field_value'):
            value = processor.get_field_value(field_name, record_id)
            self.field_cache[cache_key] = value
            return value
        
        return None
    
    def clear_cache(self):
        """清空缓存"""
        self.field_cache.clear()
        self.logger.info("🧹 缓存已清空")
    
    def get_processing_status(self) -> Dict[str, Any]:
        """获取处理状态"""
        return {
            "loaded_processors": list(self.table_processors.keys()),
            "processing_order": self.processing_order,
            "cache_size": len(self.field_cache)
        }

if __name__ == "__main__":
    # 测试核心引擎
    engine = CoreEngine()
    print("核心引擎状态:", engine.get_processing_status())
    
    # 测试数据流处理
    test_data = {
        "原始商品名称": "测试商品 100g",
        "商品信息": "测试商品信息"
    }
    
    try:
        results = engine.process_data_flow(test_data)
        print("处理结果:", results)
    except Exception as e:
        print(f"处理失败: {e}")
